.trainingViewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  z-index: 10000;
  display: flex;
  flex-direction: column;
}

.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  z-index: 10000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-left-color: #137688;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Top Bar */
.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 70px;
  flex-shrink: 0;
}

.leftSection {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.centerSection {
  display: flex;
  justify-content: center;
  flex: 1;
}

.rightSection {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}

.closeBtn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: background 0.2s;
}

.closeBtn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sessionInfo h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.sessionDetails {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
  display: block;
}

.timerSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.timeLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.timeDisplay {
  font-size: 24px;
  font-weight: bold;
  color: #4CAF50;
  font-family: 'Courier New', monospace;
}

.timeWarning {
  color: #FF5722 !important;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.startBtn, .pauseBtn, .resumeBtn, .fullscreenBtn {
  background: #137688;
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s;
  margin-left: 10px;
}

.startBtn:hover, .resumeBtn:hover, .fullscreenBtn:hover {
  background: #0f5f6b;
}

.pauseBtn {
  background: #FF9800;
}

.pauseBtn:hover {
  background: #F57C00;
}

.fullscreenBtn {
  background: #4CAF50;
}

.fullscreenBtn:hover {
  background: #45a049;
}

/* Content Area */
.contentArea {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
}

.pdfViewerContainer {
  width: 100%;
  height: 100%;
  background: #1e1e1e;
  overflow: auto;
  position: relative;
}

.pdfViewerContainer .rpv-core__viewer {
  height: 100% !important;
  background: #1e1e1e !important;
  overflow: auto !important;
}

.pdfViewerContainer .rpv-default-layout__container {
  height: 100% !important;
  background: #1e1e1e !important;
  display: flex !important;
  flex-direction: column !important;
}

.pdfViewerContainer .rpv-default-layout__main {
  flex: 1 !important;
  overflow: auto !important;
  background: #1e1e1e !important;
}

.pdfViewerContainer .rpv-core__inner-pages {
  background: #1e1e1e !important;
  overflow: auto !important;
  height: 100% !important;
}

.pdfViewerContainer .rpv-core__page-layer {
  background: white !important;
  margin: 10px auto !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

.pdfViewerContainer .rpv-core__pages-container {
  overflow: auto !important;
  height: 100% !important;
}

.pdfViewerContainer .rpv-scroll-mode__dual-page-container,
.pdfViewerContainer .rpv-scroll-mode__single-page-container {
  overflow: auto !important;
  height: 100% !important;
}

/* Ensure scrollbars are visible in fullscreen */
.pdfViewerContainer ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.pdfViewerContainer ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.pdfViewerContainer ::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 6px;
}

.pdfViewerContainer ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Additional PDF viewer scroll fixes */
.pdfViewerContainer .rpv-core__doc {
  overflow: auto !important;
  height: 100% !important;
}

.pdfViewerContainer .rpv-scroll-mode__pages-container {
  overflow: auto !important;
  max-height: none !important;
  height: 100% !important;
}

.pdfViewerContainer .rpv-default-layout__sidebar {
  background: #2d2d2d !important;
}

.pdfViewerContainer .rpv-default-layout__toolbar {
  background: #2d2d2d !important;
  border-bottom: 1px solid #444 !important;
}

/* Ensure the main content area can scroll */
.trainingViewer:fullscreen .pdfViewerContainer,
.trainingViewer:-webkit-full-screen .pdfViewerContainer,
.trainingViewer:-moz-full-screen .pdfViewerContainer {
  overflow: auto !important;
}

.trainingViewer:fullscreen .rpv-core__viewer,
.trainingViewer:-webkit-full-screen .rpv-core__viewer,
.trainingViewer:-moz-full-screen .rpv-core__viewer {
  overflow: auto !important;
}

.videoPlayer {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.noContent {
  color: white;
  text-align: center;
  font-size: 18px;
}

/* Idle Overlay */
.idleOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.idleMessage {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.idleMessage h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #FF9800;
}

.idleMessage p {
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .topBar {
    padding: 10px 15px;
    min-height: 60px;
  }

  .leftSection, .centerSection, .rightSection {
    flex: none;
  }

  .topBar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .leftSection {
    justify-content: space-between;
  }

  .centerSection {
    justify-content: center;
  }

  .rightSection {
    justify-content: center;
  }

  .sessionInfo h3 {
    font-size: 16px;
  }

  .sessionDetails {
    font-size: 12px;
  }

  .timeDisplay {
    font-size: 20px;
  }

  .idleMessage {
    padding: 30px 20px;
    margin: 20px;
  }

  .idleMessage h3 {
    font-size: 20px;
  }

  .idleMessage p {
    font-size: 14px;
  }
}

/* Fullscreen specific styles */
.trainingViewer:fullscreen {
  background: #000;
}

.trainingViewer:-webkit-full-screen {
  background: #000;
}

.trainingViewer:-moz-full-screen {
  background: #000;
}

.trainingViewer:-ms-fullscreen {
  background: #000;
}
