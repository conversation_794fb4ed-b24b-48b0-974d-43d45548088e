.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 2.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  color: #333;
  box-sizing: border-box;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.required {
  color: red;
  margin-left: 4px;
}

.formSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 6px rgba(0,0,0,0.05);
  border: 1px solid #e9ecef;
}

.formSection h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

.row textarea {
  resize: vertical;
  min-height: 80px;
}

/* Document and OJT Selection Section */
.documentOjtSection {
  margin-top: 1.5rem; /* Space above this section */
}

.documentOjtLayout {
  display: flex;
  gap: 2rem; /* Gap between the two columns */
  margin-top: 1rem;
}

.availableOjtColumn {
  flex: 0 0 30%; /* Flex-grow: 0, Flex-shrink: 0, Flex-basis: 40% */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 500px;
}

.selectedOjtColumn {
  flex: 0 0 70%; /* Flex-grow: 0, Flex-shrink: 0, Flex-basis: 60% */
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 500px;
}

.layoutDivider {
  width: 1px;
  background-color: #dee2e6; /* Light grey divider */
  flex-shrink: 0; /* Prevent shrinking */
}

.documentOjtSearch input[type="text"] {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
  box-sizing: border-box;
}

.documentOjtList {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  max-height: 600px;
  overflow-y: auto;
  background-color: #fff;
  padding: 0.5rem;
  position: relative;
}

.loadingIndicator {
  text-align: center;
  padding: 10px;
  color: #6c757d;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #e9ecef;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
}

.documentDetails {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.documentMeta {
  color: #666;
  font-size: 14px;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.documentType {
  color: #666;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9ecef;
  display: inline-flex;
  align-items: center;
}

.documentCode {
  color: #666;
  font-size: 12px;
  background-color: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9ecef;
  display: inline-flex;
  align-items: center;
}

.documentName {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2px 0;
}

.documentOjtItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  gap: 12px;
}

.documentOjtItem:last-child {
  border-bottom: none;
}

.documentOjtItem:hover {
  background-color: #f8f9fa;
}

.addItemButton {
  background: none;
  border: none;
  color: #28a745;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.noResults,
.noSelected {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-size: 0.95rem;
}

.selectedItemsContainerheadings {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Gap between bubbles */
  margin-top: 0.5rem;
  margin-right: 0.5rem;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  max-height: 600px;
  overflow-y: hidden;
  width: 90%; /* Ensure container takes full width of its parent column */
  box-sizing: border-box;
}

.selectedItemsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Gap between bubbles */
  margin-top: 0.5rem;
  margin-right: 0.5rem;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  max-height: 600px;
  overflow-y: auto;
  width: 90%; /* Ensure container takes full width of its parent column */
  box-sizing: border-box; /* Include padding and border */
}

.selectedItemsContainer h4 {
  overflow-y: hidden;
}

.selectedItemBubble {
  background-color: #fff;
  color: #333;
  padding: 6px 10px; /* Adjusted padding */
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px; /* Gap between remove button, label, and frequency options */
  font-size: 0.85rem;
  border: 1px solid #becbd2;
  flex-grow: 0; /* Prevent bubble from growing */
  flex-shrink: 0; /* Allow shrinking but prefer not to */
  width: 100%;
  justify-content: space-between;
  box-sizing: border-box;
}

.selectedItemBubble .itemLabel {
  flex-grow: 0;
  flex-shrink: 1; /* Allow label to shrink */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* Keep text on a single line */
}

.removeItemButton {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-weight: bold;
  padding-left: 15px; /* Remove padding to rely on gap */
  font-size: 1rem;
  transition: color 0.2s ease;
  flex-shrink: 0; /* Prevent button from shrinking */
  order: -1; /* Place at the beginning of the flex container */
}

.removeItemButton:hover {
  color: #c82333;
}

.frequencyOptions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 0;
  padding: 0;
  background: none;
  border: none;
  align-items: center;
  flex-shrink: 0;
}

.frequencyToggle {
  padding: 4px 12px;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  background: white;
  color: #495057;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.frequencyToggle:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.frequencyToggle.selected {
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.frequencyToggle.selected:hover {
  background: #138496;
  border-color: #138496;
}

.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.primaryBtn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 18px;
  color: #666;
}

.error {
  color: #f44336;
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

/* Department Group Styles */
.departmentGroup {
  margin-bottom: 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.departmentHeader {
  width: 100%;
  padding: 10px 12px;
  background: #f8f9fa;
  border: none;
  text-align: left;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.departmentHeader:hover {
  background: #e9ecef;
}

.expandIcon {
  font-size: 0.8rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.departmentDocs {
  background: white;
  border-top: 1px solid #e9ecef;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form {
    padding: 1rem 0.8rem;
    margin: 0;
    max-width: 100%;
    border-radius: 0;
    gap: 1rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .documentOjtLayout {
    flex-direction: column;
    gap: 1.5rem;
  }

  .layoutDivider {
    width: 100%;
    height: 1px;
  }

  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }

  .selectedItemBubble {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .frequencyOptions {
    justify-content: center;
    width: 100%;
  }
}
