import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styles from './editCoursecode.module.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchApprovedDocumentsAndOjts, updateCourseCode } from '../../../../services/course-code/CoursecodeService';
import { fetchAllDepartments } from '../../../../services/systemAdmin/DepartmentMasterService';
import { useCourse } from '../../../../context/coursecode/CourseContext';
import Modal from '../../../../components/common/Modal';

const EditCoursecode = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { courseData, loading: courseLoading, error: courseError, fetchCourseById } = useCourse();
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const itemsPerPage = 15;

  const [formData, setFormData] = useState({
    courseCode: '',
    courseTitle: '',
    department: '',
    remark: '',
  });


  const [availableDocumentsOjts, setAvailableDocumentsOjts] = useState([]);
  const [selectedDocumentsOjts, setSelectedDocumentsOjts] = useState([]);
  const [documentOjtSearchTerm, setDocumentOjtSearchTerm] = useState('');
  const [totalRecords, setTotalRecords] = useState(0);
  const [removedDocuments, setRemovedDocuments] = useState([]);
  const [removedFrequencies, setRemovedFrequencies] = useState([]);
  const [modifiedDocuments, setModifiedDocuments] = useState(new Set());
  const listRef = useRef(null);
  const debounceTimeoutRef = useRef(null);

  // Department state
  const [departments, setDepartments] = useState([]);
  const [departmentSearchTerm, setDepartmentSearchTerm] = useState('');
  const departmentSearchRef = useRef(null);
  const [departmentPage, setDepartmentPage] = useState(1);
  const [loadingDepartments, setLoadingDepartments] = useState(false);

  const frequencyOptions = ['Monthly', 'Quarterly', 'Half Yearly', 'Yearly'];

  const [expandedDepartments, setExpandedDepartments] = useState(new Set());
  const [groupedDocuments, setGroupedDocuments] = useState({});

  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');
  const initialFormDataRef = useRef(null);

  // Fetch course data on component mount
  useEffect(() => {
    if (id) {
      fetchCourseById(id);
    }
  }, [id]);

  // Update form data when course data is fetched
  useEffect(() => {
    if (courseData) {
      setFormData({
        courseCode: courseData.courseCode || '',
        courseTitle: courseData.courseTitle || '',
        department: courseData.departmentID || '',
        remark: courseData.remarks || '',
      });

      // Set selected documents and OJTs with correct action types
      const selectedItems = courseData.linkedDocuments.map(doc => ({
        value: doc.documentID !== 0 ? `doc-${doc.documentID}` : `ojt-${doc.ojtid}`,
        label: doc.name,
        description: doc.code,
        type: doc.type,
        departmentName: doc.departmentName,
        frequency: doc.frequency || '',
        transactionID: doc.transactionID || 0,
        documentID: doc.documentID || 0,
        ojtid: doc.ojtid || 0,
        originalFrequency: doc.frequency || '', // Store original frequency to track changes
        actionType: 1 // Initialize existing items with actionType 1
      }));
      setSelectedDocumentsOjts(selectedItems);

      // After setting selected items, fetch available documents
      fetchDocuments('', 0, false);
    }
  }, [courseData]);

  // Remove the useEffect for initial document fetch since we'll do it after setting selected items
  useEffect(() => {
    if (page > 0) {
      fetchDocuments(documentOjtSearchTerm, page, true);
    }
  }, [page]);

  // Update initialFormDataRef when courseData is loaded
  useEffect(() => {
    if (courseData) {
      initialFormDataRef.current = {
        courseCode: courseData.courseCode || '',
        courseTitle: courseData.courseTitle || '',
        department: courseData.departmentID || '',
        remark: courseData.remarks || '',
        selectedDocuments: courseData.linkedDocuments.map(doc => ({
          value: doc.documentID !== 0 ? `doc-${doc.documentID}` : `ojt-${doc.ojtid}`,
          label: doc.name,
          description: doc.code,
          type: doc.type,
          frequency: doc.frequency || '',
          transactionID: doc.transactionID || 0,
          documentID: doc.documentID || 0,
          ojtid: doc.ojtid || 0,
          originalFrequency: doc.frequency || '',
          actionType: 1
        }))
      };
    }
  }, [courseData]);

  // Fetch departments
  const fetchDepartments = async (searchText = '', page = 1) => {
    try {
      setLoadingDepartments(true);
      const response = await fetchAllDepartments(page, 50, searchText);
      if (response && response.departments) {
        setDepartments(response.departments.map(dept => ({
          value: dept.departmentID,
          label: dept.departmentName
        })));
      } else {
        toast.error('Failed to fetch departments');
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.error('Error loading departments');
    } finally {
      setLoadingDepartments(false);
    }
  };

  // Initial department fetch
  useEffect(() => {
    fetchDepartments();
  }, []);

  // Department search with debounce
  useEffect(() => {
    if (departmentSearchRef.current) {
      clearTimeout(departmentSearchRef.current);
    }

    departmentSearchRef.current = setTimeout(() => {
      fetchDepartments(departmentSearchTerm);
    }, 500);

    return () => {
      if (departmentSearchRef.current) {
        clearTimeout(departmentSearchRef.current);
      }
    };
  }, [departmentSearchTerm]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleDepartmentSearch = (e) => {
    setDepartmentSearchTerm(e.target.value);
  };

  const fetchDocuments = async (searchText = '', pageNumber = 0, append = false) => {
    try {
      setLoading(true);
      const payload = {
        page: {
          offset: pageNumber * itemsPerPage,
          fetch: itemsPerPage
        },
        searchText
      };

      const response = await fetchApprovedDocumentsAndOjts(payload);

      if (response.header.errorCount === 0) {
        // Filter out documents that are already selected
        const newDocuments = response.approvedDocuments.filter(
          doc => !selectedDocumentsOjts.some(
            selected => selected.value === doc.value
          )
        );

        setAvailableDocumentsOjts(prev =>
          append ? [...prev, ...newDocuments] : newDocuments
        );
        setTotalRecords(response.totalRecord);
        setHasMore(response.approvedDocuments.length === itemsPerPage);
      } else {
        toast.error('Failed to fetch documents');
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error('Error fetching documents');
    } finally {
      setLoading(false);
    }
  };

  const handleScroll = () => {
    if (listRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = listRef.current;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !loading && hasMore) {
        setPage(prev => prev + 1);
      }
    }
  };

  const handleDocumentOjtSearchChange = (e) => {
    const value = e.target.value;
    setDocumentOjtSearchTerm(value);
    setPage(0); // Reset page when search term changes

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      fetchDocuments(value, 0, false);
    }, 500);
  };

  const handleFrequencyChange = (value, frequency) => {
    setSelectedDocumentsOjts(prev => {
      const updatedDocs = prev.map(doc => {
        if (doc.value === value) {
          // If clicking the same frequency that's already selected, remove it
          if (doc.frequency === frequency) {
            // Track modification
            setModifiedDocuments(prev => new Set([...prev, doc.value]));
            return {
              ...doc,
              frequency: '', // Clear the frequency
              actionType: doc.transactionID > 0 ? 1 : 0 // Keep document with actionType 1 or 0
            };
          } else {
            // Setting a new frequency
            setModifiedDocuments(prev => new Set([...prev, doc.value]));
            return {
              ...doc,
              frequency,
              actionType: doc.transactionID > 0 ? 1 : 0
            };
          }
        }
        return doc;
      });
      return updatedDocs;
    });
  };

  const handleAddItemToSelected = (itemToAdd) => {
    setSelectedDocumentsOjts(prev => {
      // Check if item is already selected
      if (!prev.some(item => item.value === itemToAdd.value)) {
        // Track newly added items
        setModifiedDocuments(prev => new Set([...prev, itemToAdd.value]));

        // Extract documentID or ojtid from the value
        let documentID = 0;
        let ojtid = 0;

        if (itemToAdd.value.startsWith('doc-')) {
          documentID = parseInt(itemToAdd.value.replace('doc-', ''));
        } else if (itemToAdd.value.startsWith('ojt-')) {
          ojtid = parseInt(itemToAdd.value.replace('ojt-', ''));
        }

        return [...prev, {
          ...itemToAdd,
          frequency: '',
          actionType: 0,
          transactionID: 0,
          documentID: documentID,
          ojtid: ojtid,
          originalFrequency: ''
        }];
      }
      return prev;
    });

    // Remove from available documents immediately
    setAvailableDocumentsOjts(prev =>
      prev.filter(item => item.value !== itemToAdd.value)
    );
  };

  const handleRemoveItemFromSelected = (itemToRemove) => {
    // If item has a transactionID (existing document), add to removedDocuments
    if (itemToRemove.transactionID > 0) {
      setRemovedDocuments(prev => [...prev, {
        ...itemToRemove,
        actionType: -1,
        frequency: itemToRemove.frequency || ''
      }]);
      // Track removed items
      setModifiedDocuments(prev => new Set([...prev, itemToRemove.value]));
    }

    // Remove from selected items
    setSelectedDocumentsOjts(prev =>
      prev.filter(item => item.value !== itemToRemove.value)
    );

    // Add back to available documents if it matches current search term
    if (!documentOjtSearchTerm ||
        itemToRemove.label.toLowerCase().includes(documentOjtSearchTerm.toLowerCase()) ||
        itemToRemove.description?.toLowerCase().includes(documentOjtSearchTerm.toLowerCase())) {
      setAvailableDocumentsOjts(prev => [...prev, itemToRemove]);
    }
  };

  const hasChanges = () => {
    if (!initialFormDataRef.current) return false;

    // Check if form data has changed
    const formDataChanged =
      formData.courseTitle !== initialFormDataRef.current.courseTitle ||
      formData.department !== initialFormDataRef.current.department ||
      formData.remark !== initialFormDataRef.current.remark;

    // Check if documents have changed
    const documentsChanged =
      selectedDocumentsOjts.length !== initialFormDataRef.current.selectedDocuments.length ||
      selectedDocumentsOjts.some(doc => {
        const initialDoc = initialFormDataRef.current.selectedDocuments.find(d => d.value === doc.value);
        return !initialDoc || doc.frequency !== initialDoc.frequency;
      }) ||
      initialFormDataRef.current.selectedDocuments.some(doc =>
        !selectedDocumentsOjts.find(d => d.value === doc.value)
      );

    return formDataChanged || documentsChanged || modifiedDocuments.size > 0 || removedDocuments.length > 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!hasChanges()) {
      toast.info('No changes made to update.');
      return;
    }

    if (!formData.courseCode || !formData.courseTitle || !formData.department || selectedDocumentsOjts.length === 0) {
      toast.error('Please fill in all required fields and select at least one Document/OJT.');
      return;
    }

    // Show reason modal instead of submitting directly
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Please provide a reason for the change.');
      return;
    }

    try {
      const userName = sessionStorage.getItem('userName') || 'system';
      const plantID = sessionStorage.getItem('plantId') || '1';

      // Filter only modified documents and keep documents even if they have no frequency
      const modifiedCurrentDocs = selectedDocumentsOjts
        .filter(doc => modifiedDocuments.has(doc.value) || doc.transactionID === 0)
        .map(doc => ({
          transactionID: doc.transactionID,
          documentID: doc.documentID,
          ojtid: doc.ojtid,
          frequency: doc.frequency || '', // Empty string for no frequency
          actionType: doc.transactionID > 0 ? 1 : 0 // Always keep as 1 or 0, never -1
        }));

      // Only include actually removed documents (not ones with just frequency removed)
      const actuallyRemovedDocs = removedDocuments
        .filter(doc => !selectedDocumentsOjts.some(selected => selected.value === doc.value))
        .map(doc => ({
          ...doc,
          actionType: -1
        }));

      const allDocuments = [
        ...modifiedCurrentDocs,
        ...actuallyRemovedDocs
      ];

      const payload = {
        courseCodeID: parseInt(id),
        courseCode: formData.courseCode,
        courseTitle: formData.courseTitle,
        departmentID: parseInt(formData.department),
        remarks: formData.remark || '',
        activityBy: userName,
        plantID: parseInt(plantID),
        reasonforChange: reasonForChange,
        electronicSignature: userName,
        signatureDate: new Date().toISOString(),
        courseLinkedDocuments: allDocuments
      };

      console.log('Update payload:', payload); // Debug log

      const data = await updateCourseCode(payload);

      if (data?.header?.errorCount === 0) {
        toast.success(data.header.messages?.[0]?.messageText || 'Course Code updated successfully!');
        setShowReasonModal(false);
        setReasonForChange('');
        setTimeout(() => {
          navigate('/course-code/course-code-registration');
        }, 1500);
      } else {
        const errorMessage = data?.header?.messages?.[0]?.messageText || 'Failed to update Course Code.';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error updating Course Code:', error);
      toast.error('An error occurred while updating Course Code.');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  useEffect(() => {
    // Group documents by department
    const grouped = availableDocumentsOjts.reduce((acc, doc) => {
      const deptName = doc.departmentName || 'Other';
      if (!acc[deptName]) {
        acc[deptName] = [];
      }
      acc[deptName].push(doc);
      return acc;
    }, {});
    setGroupedDocuments(grouped);
  }, [availableDocumentsOjts]);

  const toggleDepartment = (deptName) => {
    setExpandedDepartments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(deptName)) {
        newSet.delete(deptName);
      } else {
        newSet.add(deptName);
      }
      return newSet;
    });
  };

  if (courseLoading) {
    return <div className={styles.loading}>Loading...</div>;
  }

  if (courseError) {
    return <div className={styles.error}>{courseError}</div>;
  }

  return (
    <div className={styles.container}>
      <ToastContainer />
      <form className={styles.form} onSubmit={handleSubmit}>
        <h3 className={styles.sectionHeading}>Course Code Registration</h3>

        {/* Course Details Section */}
        <div className={styles.formGrid}>
          <div className={styles.row}>
            <label>Course Code <span className={styles.required}>*</span></label>
            <input
              type="text"
              name="courseCode"
              value={formData.courseCode}
              onChange={handleInputChange}
              required
              readOnly
              style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
            />
          </div>

          <div className={styles.row}>
            <label>Course Title <span className={styles.required}>*</span></label>
            <input
              type="text"
              name="courseTitle"
              value={formData.courseTitle}
              onChange={handleInputChange}
              required
              placeholder="Enter course title"
            />
          </div>

          <div className={styles.row}>
            <label>Department <span className={styles.required}>*</span></label>
            <select
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Department</option>
              {departments.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>

          <div className={styles.row}>
            <label>Remarks</label>
            <textarea
              name="remark"
              value={formData.remark}
              onChange={handleInputChange}
              placeholder="Enter remarks"
            />
          </div>
        </div>

          {/* Second Container: Document and OJT Selection */}
          <div className={`${styles.formSection} ${styles.documentOjtSection}`}>
            <h3>Approved Document Selection <span className={styles.required}>*</span></h3>
            <div className={styles.documentOjtLayout}>
              {/* Left Column: Available Documents and OJTs */}
              <div className={styles.availableOjtColumn}>
                <div className={styles.documentOjtSearch}>
                  <input
                    type="text"
                    placeholder="Search Documents"
                    value={documentOjtSearchTerm}
                    onChange={handleDocumentOjtSearchChange}
                  />
                </div>
                <div
                  className={styles.documentOjtList}
                  ref={listRef}
                  onScroll={handleScroll}
                >
                  {Object.keys(groupedDocuments).length > 0 ? (
                    Object.entries(groupedDocuments).map(([deptName, docs]) => (
                      <div key={deptName} className={styles.departmentGroup}>
                        <button
                          type="button"
                          className={styles.departmentHeader}
                          onClick={() => toggleDepartment(deptName)}
                        >
                          <span className={styles.expandIcon}>
                            {expandedDepartments.has(deptName) ? '🔽' : '🔼'}
                          </span>
                          {deptName} ({docs.length})
                        </button>
                        {expandedDepartments.has(deptName) && (
                          <div className={styles.departmentDocs}>
                            {docs.map(item => (
                              <div key={item.value} className={styles.documentOjtItem}>
                                <div className={styles.documentDetails}>
                                  <div className={styles.documentMeta}>
                                    ({item.type || 'N/A'}) [{item.description || 'N/A'}]
                                  </div>
                                  <div className={styles.documentName}>{item.label || 'Untitled'}</div>
                                </div>
                                <button
                                  type="button"
                                  className={styles.addItemButton}
                                  onClick={() => handleAddItemToSelected(item)}
                                >
                                  +
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className={styles.noResults}>
                      {loading ? 'Loading...' : 'No matching Documents or OJTs available.'}
                    </div>
                  )}
                  {loading && (
                    <div className={styles.loadingIndicator}>Loading more...</div>
                  )}
                </div>
              </div>

              {/* Divider */}
              <div className={styles.layoutDivider}></div>

              {/* Right Column: Selected Documents and OJTs */}
              <div className={styles.selectedOjtColumn}>
                <div className={styles.selectedItemsContainerheadings}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>
                    <h4 style={{ margin: 0 }}>Selected Items</h4>
                    <h4 style={{ margin: 0 }}>Recurring Training Frequency</h4>
                  </div>
                </div>
                {selectedDocumentsOjts.length > 0 ? (
                  <div className={styles.selectedItemsContainer}>
                    {selectedDocumentsOjts.map(item => (
                      <div key={item.value} className={styles.selectedItemBubble}>
                        <span className={styles.itemLabel}>{item.label}</span>
                        <div className={styles.frequencyOptions}>
                          {frequencyOptions.map(frequency => (
                            <button
                              key={frequency}
                              type="button"
                              className={`${styles.frequencyToggle} ${item.frequency === frequency ? styles.selected : ''}`}
                              onClick={() => handleFrequencyChange(item.value, frequency)}
                            >
                              {frequency}
                            </button>
                          ))}
                        </div>
                        <button
                          type="button"
                          className={styles.removeItemButton}
                          onClick={() => handleRemoveItemFromSelected(item)}
                        >
                          -
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.noSelected}>No Documents or OJTs selected yet.</div>
                )}
              </div>
            </div>
          </div>

        <div className={styles.submitRow}>
          <button type="submit" className={styles.primaryBtn}>Update</button>
          <button type="button" className={styles.cancelBtn} onClick={() => navigate('/course-code/course-code-registration')}>Cancel</button>
        </div>
      </form>

      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the Course Code "{formData.courseTitle}" ({formData.courseCode})</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </div>
  );
};

export default EditCoursecode;