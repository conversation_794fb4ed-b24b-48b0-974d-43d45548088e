import React, { useState, useEffect, useRef, useCallback } from 'react';
import { FaTimes, FaPlay, FaPause } from 'react-icons/fa';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import styles from './TrainingSessionViewer.module.css';
import { downloadFileById } from '../../../services/DownloadService';
import { FetchStreamToken } from '../../../services/DownloadService';

const TrainingSessionViewer = ({ session, onClose, onTimeUpdate }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(session?.remainingTime || 600); // 10 minutes default
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [isTimerStarted, setIsTimerStarted] = useState(false);
  const [fileUrl, setFileUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [contentType, setContentType] = useState(null);
  const [isIdle, setIsIdle] = useState(false);

  const timerRef = useRef(null);
  const idleTimerRef = useRef(null);
  const lastActivityRef = useRef(Date.now());
  const viewerRef = useRef(null);
  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  // Idle detection timeout (1 minute)
  const IDLE_TIMEOUT = 60000;

  // Format time display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Reset idle timer
  const resetIdleTimer = useCallback(() => {
    lastActivityRef.current = Date.now();
    setIsIdle(false);

    if (idleTimerRef.current) {
      clearTimeout(idleTimerRef.current);
    }

    idleTimerRef.current = setTimeout(() => {
      setIsIdle(true);
      if (isTimerRunning) {
        setIsTimerRunning(false);
      }
    }, IDLE_TIMEOUT);
  }, [isTimerRunning]);

  // Handle user activity
  const handleActivity = useCallback(() => {
    resetIdleTimer();
  }, [resetIdleTimer]);

  // Timer logic
  useEffect(() => {
    if (isTimerRunning && timeRemaining > 0 && !isIdle) {
      timerRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            setIsTimerRunning(false);
            // Training completed
            onTimeUpdate?.(session.sessionID, 0, true);
            return 0;
          }
          // Update remaining time periodically
          onTimeUpdate?.(session.sessionID, newTime, false);
          return newTime;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTimerRunning, timeRemaining, isIdle, session.sessionID, onTimeUpdate]);

  // Handle visibility change (tab switching)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && isTimerRunning) {
        setIsTimerRunning(false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isTimerRunning]);

  // Custom fullscreen functions
  const enterFullscreen = useCallback(async () => {
    if (viewerRef.current) {
      try {
        if (viewerRef.current.requestFullscreen) {
          await viewerRef.current.requestFullscreen();
        } else if (viewerRef.current.webkitRequestFullscreen) {
          await viewerRef.current.webkitRequestFullscreen();
        } else if (viewerRef.current.msRequestFullscreen) {
          await viewerRef.current.msRequestFullscreen();
        }
      } catch (error) {
        console.error('Error entering fullscreen:', error);
      }
    }
  }, []);

  const exitFullscreen = useCallback(async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen();
      } else if (document.webkitFullscreenElement) {
        await document.webkitExitFullscreen();
      } else if (document.msFullscreenElement) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  }, []);

  // Handle fullscreen change
  const handleFullScreenChange = useCallback(() => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.msFullscreenElement
    );

    setIsFullscreen(isCurrentlyFullscreen);

    // If user exits fullscreen accidentally, pause timer but don't close
    if (!isCurrentlyFullscreen && isTimerRunning) {
      setIsTimerRunning(false);
      // Optionally show a message that they can re-enter fullscreen
    }
  }, [isTimerRunning]);

  // Handle window focus/blur
  useEffect(() => {
    const handleFocus = () => {
      resetIdleTimer();
    };

    const handleBlur = () => {
      if (isTimerRunning) {
        setIsTimerRunning(false);
      }
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [isTimerRunning, resetIdleTimer]);

  // Activity listeners
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [handleActivity]);

  // Load file content
  useEffect(() => {
    const fetchFile = async () => {
      if (!session?.sessionID) return;

      setLoading(true);
      try {
        const extension = session.documentExtention || '.pdf';
        setContentType(extension);

        if (extension === '.pdf') {
          const response = await downloadFileById('document', session.sessionID);
          const url = URL.createObjectURL(response.data);
          setFileUrl(url);
        } else if (extension === '.mp4') {
          const token = await FetchStreamToken('document', session.sessionID);
          setFileUrl(import.meta.env.VITE_API_BASE_URL + 'download/stream/' + token);
        }
      } catch (error) {
        console.error('Error loading file:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFile();

    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [session]);

  // Enter fullscreen on component mount and start timer
  useEffect(() => {
    const initializeViewer = async () => {
      await enterFullscreen();
      // Auto-start timer when entering fullscreen
      setTimeout(() => {
        setIsTimerStarted(true);
        setIsTimerRunning(true);
        resetIdleTimer();
      }, 1000); // Small delay to ensure fullscreen is active
    };

    initializeViewer();
  }, [enterFullscreen, resetIdleTimer]);

  // Handle fullscreen change events
  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullScreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullScreenChange);
    document.addEventListener('msfullscreenchange', handleFullScreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullScreenChange);
      document.removeEventListener('msfullscreenchange', handleFullScreenChange);
    };
  }, [handleFullScreenChange]);

  const handleClose = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    if (idleTimerRef.current) {
      clearTimeout(idleTimerRef.current);
    }

    // Exit fullscreen
    exitFullscreen();

    // Update remaining time before closing
    onTimeUpdate?.(session.sessionID, timeRemaining, false);
    onClose();
  };

  const handleStartResume = () => {
    if (!isTimerStarted) {
      setIsTimerStarted(true);
    }
    setIsTimerRunning(true);
    setIsIdle(false);
    resetIdleTimer();
  };

  const handlePause = () => {
    setIsTimerRunning(false);
  };

  if (loading) {
    return (
      <div ref={viewerRef} className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading training session...</p>
      </div>
    );
  }

  return (
    <div ref={viewerRef} className={styles.trainingViewer}>
      {/* Top Bar */}
      <div className={styles.topBar}>
        <div className={styles.leftSection}>
          <button className={styles.closeBtn} onClick={handleClose}>
            <FaTimes />
          </button>

          <div className={styles.sessionInfo}>
            <h3>{session.sessionName}</h3>
            <span className={styles.sessionDetails}>
              {session.sessionCode} | {session.sessionType} | v{session.version}
            </span>
          </div>
        </div>

        <div className={styles.centerSection}>
          <div className={styles.timerSection}>
            <span className={styles.timeLabel}>Time Remaining:</span>
            <span className={`${styles.timeDisplay} ${timeRemaining <= 60 ? styles.timeWarning : ''}`}>
              {formatTime(timeRemaining)}
            </span>
          </div>
        </div>

        <div className={styles.rightSection}>
          {!isFullscreen && (
            <button className={styles.fullscreenBtn} onClick={enterFullscreen}>
              Enter Fullscreen
            </button>
          )}
          {!isTimerStarted || !isTimerRunning ? (
            <button className={styles.startBtn} onClick={handleStartResume}>
              <FaPlay />
              {!isTimerStarted ? 'Start' : 'Resume'}
            </button>
          ) : (
            <button className={styles.pauseBtn} onClick={handlePause}>
              <FaPause />
              Pause
            </button>
          )}
        </div>
      </div>

      {/* Content Area */}
      <div className={styles.contentArea}>
        {isIdle && (
          <div className={styles.idleOverlay}>
            <div className={styles.idleMessage}>
              <h3>Session Paused</h3>
              <p>Timer paused due to inactivity</p>
              <button className={styles.resumeBtn} onClick={handleStartResume}>
                Resume Training
              </button>
            </div>
          </div>
        )}

        {contentType === '.pdf' && fileUrl && (
          <div className={styles.pdfViewerContainer}>
            <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
              <Viewer
                fileUrl={fileUrl}
                plugins={[defaultLayoutPluginInstance]}
                theme="dark"
                defaultScale="auto"
                scrollMode="vertical"
              />
            </Worker>
          </div>
        )}

        {contentType === '.mp4' && fileUrl && (
          <video
            src={fileUrl}
            className={styles.videoPlayer}
            controls
            onPlay={handleStartResume}
            onPause={handlePause}
          />
        )}

        {!fileUrl && !loading && (
          <div className={styles.noContent}>
            <p>No content available for this training session</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainingSessionViewer;
