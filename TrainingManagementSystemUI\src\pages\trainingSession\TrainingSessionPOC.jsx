import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './TrainingSessionPOC.module.css';
import { FaEdit, FaEye, FaChevronLeft, FaChevronRight, FaDownload } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import Pagination from '../../components/pagination/Pagination';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../components/common/Modal';

const getStatusClass = (status) => {
  switch ((status || '').toLowerCase()) {
    case 'underreview':
      return styles.statusDraft;
    case 'approved':
      return styles.statusApproved;
    case 'returned':
      return styles.statusReturn;
    case 'rejected':
      return styles.statusReject;
    default:
      return styles.statusDraft;
  }
};

const handleTooltipPosition = (event) => {
  const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
  if (tooltip) {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
    tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
  }
};

const TrainingSessionPOC = () => {
  const navigate = useNavigate();

  const [sessions, setSessions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;

  // Debounced search effect
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual training session API call
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm
        };
        
        // Mock data for now - replace with actual API call
        const mockData = {
          trainingSessions: [
            {
              sessionID: 1,
              sessionName: 'Safety Training Session',
              sessionCode: 'STS001',
              sessionType: 'Safety',
              version: '1.0',
              status: 'Approved'
            },
            {
              sessionID: 2,
              sessionName: 'Technical Training Session',
              sessionCode: 'TTS002',
              sessionType: 'Technical',
              version: '1.1',
              status: 'UnderReview'
            }
          ],
          totalRecord: 2
        };
        
        setSessions(mockData.trainingSessions || []);
        setTotalRecords(mockData.totalRecord || 0);
      } catch {
        setSessions([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener('scroll', () => {});
      window.addEventListener('resize', () => {});
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', () => {});
        window.removeEventListener('resize', () => {});
      }
    };
  }, [sessions]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleAddSessionClick = () => {
    // TODO: Navigate to add training session page
    toast.info('Add Training Session functionality coming soon!');
  };

  const handleEditSessionClick = async (session) => {
    try {
      // TODO: Replace with actual training session edit logic
      toast.info('Edit Training Session functionality coming soon!');
    } catch (error) {
      console.error('Error preparing session for editing:', error);
      toast.error('Failed to prepare session for editing');
    }
  };

  const handleDeleteClick = (session) => {
    setSelectedSession(session);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      // TODO: Replace with actual delete API call
      toast.success('Training session deactivated successfully');
      setShowDeleteModal(false);
      setSelectedSession(null);
      
      // Refresh the data
      const userID = sessionStorage.getItem('userID');
      const payload = {
        userID: userID ? parseInt(userID) : 0,
        page: {
          offset: (currentPage - 1) * itemsPerPage,
          fetch: itemsPerPage
        },
        searchText: debouncedSearchTerm
      };
      // TODO: Add actual refresh logic
    } catch (error) {
      console.error('Error deactivating session:', error);
      toast.error('Failed to deactivate session');
    } finally {
      setShowDeleteModal(false);
      setSelectedSession(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedSession(null);
  };

  const handleScroll = (direction) => {
    const container = tableContainerRef.current;
    if (!container) return;
    const scrollAmount = 200;
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else if (direction === 'right') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const handleViewSession = (session) => {
    // TODO: Implement view functionality
    toast.info('View Training Session functionality coming soon!');
  };

  const handleDownloadSession = async (sessionId) => {
    try {
      // TODO: Implement download functionality
      toast.info('Download Training Session functionality coming soon!');
    } catch (error) {
      console.error('Error downloading session:', error);
      toast.error('Failed to download session');
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.sessionMaster}>
          <div className={styles.panelHeader}>
            <h2>Training Session POC</h2>
            <br /><br />
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
              <button className={styles.addSessionBtn} onClick={handleAddSessionClick}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.sessionTableContainer} ref={tableContainerRef}>
            <table className={styles.sessionTable}>
              <thead>
                <tr>
                  <th>Session Name</th>
                  <th>Session Code</th>
                  <th>Session Type</th>
                  <th>Version</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : sessions.length > 0 ? (
                  sessions.map((session) => (
                    <tr key={session.sessionID}>
                      <td>{session.sessionName}</td>
                      <td>{session.sessionCode}</td>
                      <td>{session.sessionType}</td>
                      <td>{session.version}</td>
                      <td>
                        <div
                          className={styles.tooltipWrapper}
                          onMouseEnter={handleTooltipPosition}
                        >
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(session.status)}`}
                          >
                            {session.status}
                          </span>
                          <span className={styles.tooltipText}>
                            {session.remark || session.approvalRemarks || 'No remarks available'}
                          </span>
                        </div>
                      </td>
                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleViewSession(session)}
                            title="View Session"
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleDownloadSession(session.sessionID)}
                            title="Download Session"
                          >
                            <FaDownload className={styles.downloadIcon} />
                            <span>Download</span>
                          </button>
                          <span className={styles.actionDivider}></span>
                          <button
                            className={`${styles.editBtn} ${session.status === 'Approved' ? styles.disabledBtn : ''}`}
                            onClick={() => handleEditSessionClick(session)}
                            title={session.status === 'Approved' ? 'Cannot edit approved session' : 'Edit Session'}
                            disabled={session.status === 'Approved' || session.status === 'Rejected'}
                          >
                            <FaEdit className={`${styles.editIcon} ${session.status === 'Approved' || session.status === 'Rejected' ? styles.disabledIcon : ''}`} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.editBtn}
                            onClick={() => handleDeleteClick(session)}
                            title="Deactivate Session"
                          >
                            <FcCancel className={`${styles.deleteIcon}`} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      No training sessions found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              paginate={paginate}
            />
          </div>
        </div>
      </div>
      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate training session "${selectedSession?.sessionName}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
    </>
  );
};

export default TrainingSessionPOC;
