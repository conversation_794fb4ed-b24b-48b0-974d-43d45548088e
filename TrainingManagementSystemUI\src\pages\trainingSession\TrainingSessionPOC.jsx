import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './TrainingSessionPOC.module.css';
import { FaEdit, FaEye, FaChevronLeft, FaChevronRight, FaDownload } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import Pagination from '../../components/pagination/Pagination';
import { useNavigate } from 'react-router-dom';
import { fetchDocumentsByUserId, deleteDocument, fetchDocumentById } from '../../services/sopojt-Management/DocumentRegistrationService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../components/common/Modal';
import {downloadFileToBrowserById} from '../../services/DownloadService';
import TrainingSessionViewer from '../../components/common/TrainingSessionViewer/TrainingSessionViewer';

const getStatusClass = (status) => {
  switch ((status || '').toLowerCase()) {
    case 'underreview':
      return styles.statusDraft;
    case 'approved':
      return styles.statusApproved;
    case 'returned':
      return styles.statusReturn;
    case 'rejected':
      return styles.statusReject;
    default:
      return styles.statusDraft;
  }
};

const handleTooltipPosition = (event) => {
  const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
  if (tooltip) {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
    tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
  }
};

const TrainingSessionPOC = () => {
  const navigate = useNavigate();

  const [sessions, setSessions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showTrainingViewer, setShowTrainingViewer] = useState(false);
  const [selectedTrainingSession, setSelectedTrainingSession] = useState(null);
  const [viewerKey, setViewerKey] = useState(Date.now());
  const [sessionTimes, setSessionTimes] = useState({}); // Track remaining times for each session
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;

  // Helper function to format time display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle time updates from the training viewer
  const handleTimeUpdate = (sessionID, remainingTime, isCompleted) => {
    setSessionTimes(prev => ({
      ...prev,
      [sessionID]: remainingTime
    }));

    if (isCompleted) {
      toast.success('Training session completed!');
      // Optionally update session status or trigger other actions
    }
  };

  // Debounced search effect
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm
        };

        // Using document API as placeholder for training sessions
        const res = await fetchDocumentsByUserId(payload);

        // Map document data to session format for display
        const mappedSessions = (res.documentMasters || []).map(doc => ({
          sessionID: doc.documentID,
          sessionName: doc.documentName,
          sessionCode: doc.documentCode,
          sessionType: doc.documentType,
          version: doc.documentVersion,
          status: doc.documentStatus,
          documentExtention: doc.documentExtention,
          remark: doc.remark,
          approvalRemarks: doc.approvalRemarks,
          remainingTime: sessionTimes[doc.documentID] || 600 // 10 minutes default
        }));

        setSessions(mappedSessions);
        setTotalRecords(res.totalRecord || 0);
      } catch {
        setSessions([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener('scroll', () => {});
      window.addEventListener('resize', () => {});
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', () => {});
        window.removeEventListener('resize', () => {});
      }
    };
  }, [sessions]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleAddSessionClick = () => {
    // TODO: Navigate to add training session page
    toast.info('Add Training Session functionality coming soon!');
  };

  const handleEditSessionClick = async (session) => {
    try {
      // TODO: Replace with actual training session edit logic
      toast.info('Edit Training Session functionality coming soon!');
    } catch (error) {
      console.error('Error preparing session for editing:', error);
      toast.error('Failed to prepare session for editing');
    }
  };

  const handleDeleteClick = (session) => {
    setSelectedSession(session);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      const userId = sessionStorage.getItem('userID');

      // Using document delete API as placeholder
      const response = await deleteDocument(selectedSession.sessionID, userId);

      if (response.header.errorCount === 0) {
        toast.success(response.header.messages[0].messageText || 'Training session deactivated successfully');
        setShowDeleteModal(false);
        setSelectedSession(null);

        // Refresh the data
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm
        };
        const fetchRes = await fetchDocumentsByUserId(payload);

        // Map document data to session format for display
        const mappedSessions = (fetchRes.documentMasters || []).map(doc => ({
          sessionID: doc.documentID,
          sessionName: doc.documentName,
          sessionCode: doc.documentCode,
          sessionType: doc.documentType,
          version: doc.documentVersion,
          status: doc.documentStatus,
          documentExtention: doc.documentExtention,
          remark: doc.remark,
          approvalRemarks: doc.approvalRemarks
        }));

        setSessions(mappedSessions);
        setTotalRecords(fetchRes.totalRecord || 0);
      } else {
        toast.error(response.header.messages[0].messageText || 'Failed to deactivate training session');
      }
    } catch (error) {
      console.error('Error deactivating session:', error);
      toast.error('Failed to deactivate session');
    } finally {
      setShowDeleteModal(false);
      setSelectedSession(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedSession(null);
  };

  const handleScroll = (direction) => {
    const container = tableContainerRef.current;
    if (!container) return;
    const scrollAmount = 200;
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else if (direction === 'right') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const handleViewSession = (session) => {
    setSelectedTrainingSession(session);
    setViewerKey(Date.now());
    setShowTrainingViewer(true);
  };

  const handleCloseTrainingViewer = () => {
    setShowTrainingViewer(false);
    setSelectedTrainingSession(null);
  };

  const handleDownloadSession = async (sessionId) => {
    try {
      await downloadFileToBrowserById('document', sessionId);
    } catch (error) {
      console.error('Error downloading session:', error);
      toast.error('Failed to download session');
    }
  };



  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.sessionMaster}>
          <div className={styles.panelHeader}>
            <h2>Training Session POC</h2>
            <br /><br />
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
              <button className={styles.addSessionBtn} onClick={handleAddSessionClick}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.sessionTableContainer} ref={tableContainerRef}>
            <table className={styles.sessionTable}>
              <thead>
                <tr>
                  <th>Session Name</th>
                  <th>Session Code</th>
                  <th>Session Type</th>
                  <th>Version</th>
                  <th>Status</th>
                  <th>Remaining Time</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="7" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : sessions.length > 0 ? (
                  sessions.map((session) => (
                    <tr key={session.sessionID}>
                      <td>{session.sessionName}</td>
                      <td>{session.sessionCode}</td>
                      <td>{session.sessionType}</td>
                      <td>{session.version}</td>
                      <td>
                        <div
                          className={styles.tooltipWrapper}
                          onMouseEnter={handleTooltipPosition}
                        >
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(session.status)}`}
                          >
                            {session.status}
                          </span>
                          <span className={styles.tooltipText}>
                            {session.remark || session.approvalRemarks || 'No remarks available'}
                          </span>
                        </div>
                      </td>
                      <td>
                        <span className={`${styles.timeDisplay} ${session.remainingTime <= 60 ? styles.timeWarning : ''}`}>
                          {formatTime(session.remainingTime)}
                        </span>
                      </td>
                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleViewSession(session)}
                            title="View Session"
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleDownloadSession(session.sessionID)}
                            title="Download Session"
                          >
                            <FaDownload className={styles.downloadIcon} />
                            <span>Download</span>
                          </button>
                          <span className={styles.actionDivider}></span>
                          <button
                            className={`${styles.editBtn} ${session.status === 'Approved' ? styles.disabledBtn : ''}`}
                            onClick={() => handleEditSessionClick(session)}
                            title={session.status === 'Approved' ? 'Cannot edit approved session' : 'Edit Session'}
                            disabled={session.status === 'Approved' || session.status === 'Rejected'}
                          >
                            <FaEdit className={`${styles.editIcon} ${session.status === 'Approved' || session.status === 'Rejected' ? styles.disabledIcon : ''}`} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.editBtn}
                            onClick={() => handleDeleteClick(session)}
                            title="Deactivate Session"
                          >
                            <FcCancel className={`${styles.deleteIcon}`} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '20px' }}>
                      No training sessions found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              paginate={paginate}
            />
          </div>
        </div>
      </div>
      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate training session "${selectedSession?.sessionName}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
      {showTrainingViewer && selectedTrainingSession && (
        <TrainingSessionViewer
          key={viewerKey}
          session={selectedTrainingSession}
          onClose={handleCloseTrainingViewer}
          onTimeUpdate={handleTimeUpdate}
        />
      )}
    </>
  );
};

export default TrainingSessionPOC;
